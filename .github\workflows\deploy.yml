name: CICD

on:
  push:
    branches: [ main ]

  workflow_dispatch:

env:
  # Postgres
  POSTGRESQL_VERSION: 13.1
  POSTGRESQL_DB: amigoscode
  POSTGRESQL_USER: postgres
  POSTGRESQL_PASSWORD: password
  # Java
  JAVA_VERSION: 1.15
  # DockerHub
  DOCKER_HUB_USERNAME: amigoscode
  # AWS & Elastic Bean Stalk
  AWS_REGION: eu-west-1
  EB_ENVIRONMENT_NAME: Springbootreactfullstack-env
  EB_APPLICATION_NAME: springboot-react-fullstack
  EB_DEPLOYMENT_PACKAGE: elasticbeanstalk/docker-compose.yml

jobs:
  deploy:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13.1
        env:
          POSTGRES_DB: ${{ env.POSTGRESQL_DB }}
          POSTGRES_USER: ${{ env.POSTGRESQL_USER }}
          POSTGRES_PASSWORD: ${{ env.POSTGRESQL_PASSWORD }}
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
    steps:
      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
          text: 'CICD ongoing... :eyes:'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - uses: actions/checkout@v2

      - name: Setup Java JDK
        uses: actions/setup-java@v1.4.3
        with:
          java-version: ${{ env.JAVA_VERSION }}

      - name: Build Number
        id: build-number
        run: echo "::set-output name=BUILD_NUMBER::$(date '+%-d.%-m.%Y.%-H.%-M.%-S')"

      - name: Docker Login
        uses: docker/login-action@f3364599c6aa293cdc2b8391b1b56d0c30e45c8a
        with:
          username: ${{ env.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_PASSWORD }}

      - name: Maven Clean Package and Push to Dockerhub
        run: |
          ./mvnw --no-transfer-progress clean package \
            -P build-frontend \
            -P jib-push-to-dockerhub \
              -Dapp.image.tag=${{steps.build-number.outputs.BUILD_NUMBER}}
      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
          text: ':white_check_mark: pushed amigoscode/springboot-react-fullstack:${{steps.build-number.outputs.BUILD_NUMBER}} to docker hub... https://hub.docker.com/repository/docker/amigoscode/springboot-react-fullstack'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      - name: Update and commit app version in docker-compose.yaml
        run: |
          BUILD_NUMBER=${{steps.build-number.outputs.BUILD_NUMBER}}
          echo -e "Current elasticbeanstalk/docker-compose.yml\n$(cat elasticbeanstalk/docker-compose.yml)"
          sed -i -E 's_(amigoscode/springboot-react-fullstack:)([^"]*)_\1'${BUILD_NUMBER}'_' elasticbeanstalk/docker-compose.yml
          echo -e "Current elasticbeanstalk/docker-compose.yml\n$(cat elasticbeanstalk/docker-compose.yml)"
          git config user.name github-actions
          git config user.email <EMAIL>
          git add elasticbeanstalk/docker-compose.yml
          git commit -m "new app version: ${BUILD_NUMBER}"
          git push

      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
          text: ':elasticbeanstalk: :aws: Deployment started... :grimacing: :crossed_fingers:'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      - name: Beanstalk Deploy
        uses: einaregilsson/beanstalk-deploy@ebe3476a4ce991d54336935e75e78dd9d86f9408
        with:
          aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          region: ${{ env.AWS_REGION }}
          environment_name: ${{ env.EB_ENVIRONMENT_NAME }}
          application_name: ${{ env.EB_APPLICATION_NAME }}
          deployment_package: ${{ env.EB_DEPLOYMENT_PACKAGE }}
          version_label: ${{ steps.build-number.outputs.BUILD_NUMBER }}
          version_description: Version ${{steps.build-number.outputs.BUILD_NUMBER}} deployed via github actions ${{ github.sha }}
          wait_for_deployment: 60
      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took
          text: ':tada: :arrow_right: http://springbootreactfullstack-env.eba-3imjbkem.eu-west-1.elasticbeanstalk.com'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        if: always()