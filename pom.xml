<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.4.3</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.example</groupId>
	<artifactId>demo</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>demo</name>
	<description>Demo project for Spring Boot</description>
	<properties>
		<java.version>15</java.version>
		<app.image.name>springboot-react-fullstack</app.image.name>
		<app.image.tag/>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.google.cloud.tools</groupId>
				<artifactId>jib-maven-plugin</artifactId>
				<version>2.5.2</version>
				<configuration>
					<from>
						<image>openjdk:15</image>
					</from>
					<container>
						<ports>
							<port>8080</port>
						</ports>
						<format>OCI</format>
					</container>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<profiles>
		<profile>
			<id>jib-push-to-dockerhub</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>com.google.cloud.tools</groupId>
						<artifactId>jib-maven-plugin</artifactId>
						<version>2.5.2</version>
						<configuration>
							<from>
								<image>openjdk:15</image>
							</from>
							<container>
								<ports>
									<port>8080</port>
								</ports>
								<format>OCI</format>
							</container>
						</configuration>
						<executions>
							<execution>
								<id>push-custom-tag</id>
								<phase>package</phase>
								<configuration>
									<to>
										<image>docker.io/amigoscode/${app.image.name}:${app.image.tag}</image>
									</to>
								</configuration>
								<goals>
									<goal>build</goal>
								</goals>
							</execution>
							<execution>
								<id>push-latest-tag</id>
								<phase>package</phase>
								<configuration>
									<to>
										<image>docker.io/amigoscode/${app.image.name}:latest</image>
									</to>
								</configuration>
								<goals>
									<goal>build</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>jib-push-to-local</id>
			<activation>
				<activeByDefault>false</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>com.google.cloud.tools</groupId>
						<artifactId>jib-maven-plugin</artifactId>
						<version>2.5.2</version>
						<configuration>
							<from>
								<image>openjdk:15</image>
							</from>
							<container>
								<ports>
									<port>8080</port>
								</ports>
								<format>OCI</format>
							</container>
						</configuration>
						<executions>
							<execution>
								<id>push-custom-tag</id>
								<phase>package</phase>
								<configuration>
									<to>
										<image>amigoscode/${app.image.name}:${app.image.tag}</image>
									</to>
								</configuration>
								<goals>
									<goal>dockerBuild</goal>
								</goals>
							</execution>
							<execution>
								<id>push-latest-tag</id>
								<phase>package</phase>
								<configuration>
									<to>
										<image>amigoscode/${app.image.name}:latest</image>
									</to>
								</configuration>
								<goals>
									<goal>dockerBuild</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>build-frontend</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>com.github.eirslett</groupId>
						<artifactId>frontend-maven-plugin</artifactId>
						<!-- Use the latest released version:
                        https://repo1.maven.org/maven2/com/github/eirslett/frontend-maven-plugin/ -->
						<version>1.11.2</version>
						<configuration>
							<nodeVersion>v4.6.0</nodeVersion>
							<workingDirectory>src/frontend</workingDirectory>
						</configuration>
						<executions>
							<execution>
								<id>install node and npm</id>
								<goals>
									<goal>install-node-and-npm</goal>
								</goals>
								<configuration>
									<nodeVersion>v15.4.0</nodeVersion>
									<npmVersion>7.3.0</npmVersion>
								</configuration>
							</execution>
							<execution>
								<id>npm install</id>
								<goals>
									<goal>npm</goal>
								</goals>
								<configuration>
									<arguments>install</arguments>
								</configuration>
							</execution>
							<execution>
								<id>npm run build</id>
								<goals>
									<goal>npm</goal>
								</goals>
								<configuration>
									<arguments>run build</arguments>
								</configuration>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<artifactId>maven-resources-plugin</artifactId>
						<executions>
							<execution>
								<id>copy-build-folder</id>
								<phase>process-classes</phase>
								<goals>
									<goal>copy-resources</goal>
								</goals>
								<configuration>
									<resources>
										<resource>
											<directory>src/frontend/build</directory>
										</resource>

									</resources>
									<outputDirectory>${basedir}/target/classes/static</outputDirectory>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>
</project>
