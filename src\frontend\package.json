{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.5.0", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@testing-library/user-event": "^12.7.3", "antd": "^4.13.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-scripts": "4.0.3", "unfetch": "^4.2.0", "web-vitals": "^1.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}